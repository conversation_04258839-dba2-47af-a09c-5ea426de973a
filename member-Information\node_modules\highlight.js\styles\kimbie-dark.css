pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*
    Name:     <PERSON><PERSON> (dark)
    Author:   <PERSON>
    License:  Creative Commons Attribution-ShareAlike 4.0 Unported License
    URL:      https://github.com/idleberg/<PERSON><PERSON>-highlight.js
*/
.hljs {
  background: #221a0f;
  color: #d3af86
}
/* <PERSON><PERSON> Comment */
.hljs-comment,
.hljs-quote {
  color: #d6baad
}
/* <PERSON>bie Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-meta {
  color: #dc3958
}
/* Kimbie Orange */
.hljs-number,
.hljs-built_in,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-deletion,
.hljs-link {
  color: #f79a32
}
/* <PERSON><PERSON> Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
  color: #889b4a
}
/* <PERSON><PERSON> Purple */
.hljs-keyword,
.hljs-selector-tag,
.hljs-function {
  color: #98676a
}
/* Kimbie Yellow */
.hljs-title,
.hljs-section,
.hljs-attribute {
  color: #f06431
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}