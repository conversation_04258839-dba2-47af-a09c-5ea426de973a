"use strict";
exports.ko = void 0;
var _index = require("./ko/_lib/formatDistance.js");
var _index2 = require("./ko/_lib/formatLong.js");
var _index3 = require("./ko/_lib/formatRelative.js");
var _index4 = require("./ko/_lib/localize.js");
var _index5 = require("./ko/_lib/match.js");

/**
 * @category Locales
 * @summary Korean locale.
 * @language Korean
 * @iso-639-2 kor
 * <AUTHOR> [@angdev](https://github.com/angdev)
 * <AUTHOR> [@iamssen](https://github.com/iamssen)
 * <AUTHOR> [@so99ynoodles](https://github.com/so99ynoodles)
 */
const ko = (exports.ko = {
  code: "ko",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
