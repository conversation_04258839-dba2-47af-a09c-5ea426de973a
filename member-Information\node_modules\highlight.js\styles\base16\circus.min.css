/*!
  Theme: Circus
  Author: <PERSON> (https://github.com/stepchowfun) and <PERSON> (https://github.com/ewang12)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#a7a7a7;background:#191919}.hljs ::selection,.hljs::selection{background-color:#303030;color:#a7a7a7}.hljs-comment{color:#5f5a60}.hljs-tag{color:#505050}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#a7a7a7}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#dc657d}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#4bb1a7}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#c3ba63}.hljs-strong{font-weight:700;color:#c3ba63}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#84b97c}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#4bb1a7}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#639ee4}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#b888e2}.hljs-emphasis{color:#b888e2;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b888e2}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}