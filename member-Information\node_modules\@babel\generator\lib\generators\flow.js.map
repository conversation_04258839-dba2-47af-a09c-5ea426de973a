{"version": 3, "names": ["_t", "require", "_modules", "_index", "_types2", "isDeclareExportDeclaration", "isStatement", "AnyTypeAnnotation", "word", "ArrayTypeAnnotation", "node", "print", "elementType", "token", "BooleanTypeAnnotation", "BooleanLiteralTypeAnnotation", "value", "NullLiteralTypeAnnotation", "DeclareClass", "parent", "space", "_interfaceish", "DeclareFunction", "id", "typeAnnotation", "predicate", "semicolon", "InferredPredicate", "DeclaredPredicate", "DeclareInterface", "InterfaceDeclaration", "DeclareModule", "body", "DeclareModuleExports", "DeclareTypeAlias", "TypeAlias", "DeclareOpaqueType", "OpaqueType", "DeclareVariable", "DeclareExportDeclaration", "default", "FlowExportDeclaration", "call", "DeclareExportAllDeclaration", "ExportAllDeclaration", "EnumDeclaration", "enumExplicitType", "context", "name", "hasExplicitType", "enumBody", "members", "indent", "newline", "member", "hasUnknownMembers", "dedent", "EnumBooleanBody", "explicitType", "EnumNumberBody", "EnumStringBody", "EnumSymbolBody", "EnumDefaultedMember", "enumInitializedMember", "init", "EnumBooleanMember", "EnumNumberMember", "EnumStringMember", "declaration", "declar", "specifiers", "length", "printList", "source", "ExistsTypeAnnotation", "FunctionTypeAnnotation", "typeParameters", "this", "params", "rest", "type", "method", "returnType", "FunctionTypeParam", "optional", "InterfaceExtends", "_node$extends", "extends", "_node$mixins", "_node$implements", "mixins", "implements", "_variance", "_node$variance", "kind", "variance", "andSeparator", "occurrenceCount", "InterfaceTypeAnnotation", "_node$extends2", "IntersectionTypeAnnotation", "printJoin", "types", "undefined", "MixedTypeAnnotation", "EmptyTypeAnnotation", "NullableTypeAnnotation", "NumberTypeAnnotation", "StringTypeAnnotation", "ThisTypeAnnotation", "TupleTypeAnnotation", "TypeofTypeAnnotation", "argument", "right", "TypeAnnotation", "tokenContext", "TokenContext", "arrowFlowReturnType", "TypeParameterInstantiation", "TypeParameter", "bound", "supertype", "impltype", "ObjectTypeAnnotation", "exact", "props", "properties", "callProperties", "indexers", "internalSlots", "addNewlines", "leading", "inexact", "ObjectTypeInternalSlot", "static", "ObjectTypeCallProperty", "ObjectTypeIndexer", "key", "ObjectTypeProperty", "proto", "ObjectTypeSpreadProperty", "QualifiedTypeIdentifier", "qualification", "SymbolTypeAnnotation", "orSeparator", "UnionTypeAnnotation", "TypeCastExpression", "expression", "<PERSON><PERSON><PERSON>", "VoidTypeAnnotation", "IndexedAccessType", "objectType", "indexType", "OptionalIndexedAccessType"], "sources": ["../../src/generators/flow.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport { isDeclareExportDeclaration, isStatement } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { ExportAllDeclaration } from \"./modules.ts\";\nimport { TokenContext } from \"../node/index.ts\";\n\nexport function AnyTypeAnnotation(this: Printer) {\n  this.word(\"any\");\n}\n\nexport function ArrayTypeAnnotation(\n  this: Printer,\n  node: t.ArrayTypeAnnotation,\n) {\n  this.print(node.elementType, true);\n  this.token(\"[\");\n  this.token(\"]\");\n}\n\nexport function BooleanTypeAnnotation(this: Printer) {\n  this.word(\"boolean\");\n}\n\nexport function BooleanLiteralTypeAnnotation(\n  this: Printer,\n  node: t.BooleanLiteralTypeAnnotation,\n) {\n  this.word(node.value ? \"true\" : \"false\");\n}\n\nexport function NullLiteralTypeAnnotation(this: Printer) {\n  this.word(\"null\");\n}\n\nexport function DeclareClass(\n  this: Printer,\n  node: t.DeclareClass,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"class\");\n  this.space();\n  this._interfaceish(node);\n}\n\nexport function DeclareFunction(\n  this: Printer,\n  node: t.DeclareFunction,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"function\");\n  this.space();\n  this.print(node.id);\n  // @ts-ignore(Babel 7 vs Babel 8) TODO(Babel 8) Remove this comment, since we'll remove the Noop node\n  this.print(node.id.typeAnnotation.typeAnnotation);\n\n  if (node.predicate) {\n    this.space();\n    this.print(node.predicate);\n  }\n\n  this.semicolon();\n}\n\nexport function InferredPredicate(this: Printer) {\n  this.token(\"%\");\n  this.word(\"checks\");\n}\n\nexport function DeclaredPredicate(this: Printer, node: t.DeclaredPredicate) {\n  this.token(\"%\");\n  this.word(\"checks\");\n  this.token(\"(\");\n  this.print(node.value);\n  this.token(\")\");\n}\n\nexport function DeclareInterface(this: Printer, node: t.DeclareInterface) {\n  this.word(\"declare\");\n  this.space();\n  this.InterfaceDeclaration(node);\n}\n\nexport function DeclareModule(this: Printer, node: t.DeclareModule) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"module\");\n  this.space();\n  this.print(node.id);\n  this.space();\n  this.print(node.body);\n}\n\nexport function DeclareModuleExports(\n  this: Printer,\n  node: t.DeclareModuleExports,\n) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"module\");\n  this.token(\".\");\n  this.word(\"exports\");\n  this.print(node.typeAnnotation);\n}\n\nexport function DeclareTypeAlias(this: Printer, node: t.DeclareTypeAlias) {\n  this.word(\"declare\");\n  this.space();\n  this.TypeAlias(node);\n}\n\nexport function DeclareOpaqueType(\n  this: Printer,\n  node: t.DeclareOpaqueType,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.OpaqueType(node);\n}\n\nexport function DeclareVariable(\n  this: Printer,\n  node: t.DeclareVariable,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"var\");\n  this.space();\n  this.print(node.id);\n  this.print(node.id.typeAnnotation);\n  this.semicolon();\n}\n\nexport function DeclareExportDeclaration(\n  this: Printer,\n  node: t.DeclareExportDeclaration,\n) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"export\");\n  this.space();\n  if (node.default) {\n    this.word(\"default\");\n    this.space();\n  }\n\n  FlowExportDeclaration.call(this, node);\n}\n\nexport function DeclareExportAllDeclaration(\n  this: Printer,\n  node: t.DeclareExportAllDeclaration,\n) {\n  this.word(\"declare\");\n  this.space();\n  ExportAllDeclaration.call(this, node);\n}\n\nexport function EnumDeclaration(this: Printer, node: t.EnumDeclaration) {\n  const { id, body } = node;\n  this.word(\"enum\");\n  this.space();\n  this.print(id);\n  this.print(body);\n}\n\nfunction enumExplicitType(\n  context: Printer,\n  name: string,\n  hasExplicitType: boolean,\n) {\n  if (hasExplicitType) {\n    context.space();\n    context.word(\"of\");\n    context.space();\n    context.word(name);\n  }\n  context.space();\n}\n\nfunction enumBody(context: Printer, node: t.EnumBody) {\n  const { members } = node;\n  context.token(\"{\");\n  context.indent();\n  context.newline();\n  for (const member of members) {\n    context.print(member);\n    context.newline();\n  }\n  if (node.hasUnknownMembers) {\n    context.token(\"...\");\n    context.newline();\n  }\n  context.dedent();\n  context.token(\"}\");\n}\n\nexport function EnumBooleanBody(this: Printer, node: t.EnumBooleanBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"boolean\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumNumberBody(this: Printer, node: t.EnumNumberBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"number\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumStringBody(this: Printer, node: t.EnumStringBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"string\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumSymbolBody(this: Printer, node: t.EnumSymbolBody) {\n  enumExplicitType(this, \"symbol\", true);\n  enumBody(this, node);\n}\n\nexport function EnumDefaultedMember(\n  this: Printer,\n  node: t.EnumDefaultedMember,\n) {\n  const { id } = node;\n  this.print(id);\n  this.token(\",\");\n}\n\nfunction enumInitializedMember(\n  context: Printer,\n  node: t.EnumBooleanMember | t.EnumNumberMember | t.EnumStringMember,\n) {\n  context.print(node.id);\n  context.space();\n  context.token(\"=\");\n  context.space();\n  context.print(node.init);\n  context.token(\",\");\n}\n\nexport function EnumBooleanMember(this: Printer, node: t.EnumBooleanMember) {\n  enumInitializedMember(this, node);\n}\n\nexport function EnumNumberMember(this: Printer, node: t.EnumNumberMember) {\n  enumInitializedMember(this, node);\n}\n\nexport function EnumStringMember(this: Printer, node: t.EnumStringMember) {\n  enumInitializedMember(this, node);\n}\n\nfunction FlowExportDeclaration(\n  this: Printer,\n  node: t.DeclareExportDeclaration,\n) {\n  if (node.declaration) {\n    const declar = node.declaration;\n    this.print(declar);\n    if (!isStatement(declar)) this.semicolon();\n  } else {\n    this.token(\"{\");\n    if (node.specifiers.length) {\n      this.space();\n      this.printList(node.specifiers);\n      this.space();\n    }\n    this.token(\"}\");\n\n    if (node.source) {\n      this.space();\n      this.word(\"from\");\n      this.space();\n      this.print(node.source);\n    }\n\n    this.semicolon();\n  }\n}\n\nexport function ExistsTypeAnnotation(this: Printer) {\n  this.token(\"*\");\n}\n\nexport function FunctionTypeAnnotation(\n  this: Printer,\n  node: t.FunctionTypeAnnotation,\n  parent?: t.Node,\n) {\n  this.print(node.typeParameters);\n  this.token(\"(\");\n\n  if (node.this) {\n    this.word(\"this\");\n    this.token(\":\");\n    this.space();\n    this.print(node.this.typeAnnotation);\n    if (node.params.length || node.rest) {\n      this.token(\",\");\n      this.space();\n    }\n  }\n\n  this.printList(node.params);\n\n  if (node.rest) {\n    if (node.params.length) {\n      this.token(\",\");\n      this.space();\n    }\n    this.token(\"...\");\n    this.print(node.rest);\n  }\n\n  this.token(\")\");\n\n  // this node type is overloaded, not sure why but it makes it EXTREMELY annoying\n\n  const type = parent?.type;\n  if (\n    type != null &&\n    (type === \"ObjectTypeCallProperty\" ||\n      type === \"ObjectTypeInternalSlot\" ||\n      type === \"DeclareFunction\" ||\n      (type === \"ObjectTypeProperty\" && parent.method))\n  ) {\n    this.token(\":\");\n  } else {\n    this.space();\n    this.token(\"=>\");\n  }\n\n  this.space();\n  this.print(node.returnType);\n}\n\nexport function FunctionTypeParam(this: Printer, node: t.FunctionTypeParam) {\n  this.print(node.name);\n  if (node.optional) this.token(\"?\");\n  if (node.name) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.typeAnnotation);\n}\n\nexport function InterfaceExtends(this: Printer, node: t.InterfaceExtends) {\n  this.print(node.id);\n  this.print(node.typeParameters, true);\n}\n\nexport {\n  InterfaceExtends as ClassImplements,\n  InterfaceExtends as GenericTypeAnnotation,\n};\n\nexport function _interfaceish(\n  this: Printer,\n  node: t.InterfaceDeclaration | t.DeclareInterface | t.DeclareClass,\n) {\n  this.print(node.id);\n  this.print(node.typeParameters);\n  if (node.extends?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(node.extends);\n  }\n  if (node.type === \"DeclareClass\") {\n    if (node.mixins?.length) {\n      this.space();\n      this.word(\"mixins\");\n      this.space();\n      this.printList(node.mixins);\n    }\n    if (node.implements?.length) {\n      this.space();\n      this.word(\"implements\");\n      this.space();\n      this.printList(node.implements);\n    }\n  }\n  this.space();\n  this.print(node.body);\n}\n\nexport function _variance(\n  this: Printer,\n  node:\n    | t.TypeParameter\n    | t.ObjectTypeIndexer\n    | t.ObjectTypeProperty\n    | t.ClassProperty\n    | t.ClassPrivateProperty\n    | t.ClassAccessorProperty,\n) {\n  const kind = node.variance?.kind;\n  if (kind != null) {\n    if (kind === \"plus\") {\n      this.token(\"+\");\n    } else if (kind === \"minus\") {\n      this.token(\"-\");\n    }\n  }\n}\n\nexport function InterfaceDeclaration(\n  this: Printer,\n  node: t.InterfaceDeclaration | t.DeclareInterface,\n) {\n  this.word(\"interface\");\n  this.space();\n  this._interfaceish(node);\n}\n\nfunction andSeparator(this: Printer, occurrenceCount: number) {\n  this.space();\n  this.token(\"&\", false, occurrenceCount);\n  this.space();\n}\n\nexport function InterfaceTypeAnnotation(\n  this: Printer,\n  node: t.InterfaceTypeAnnotation,\n) {\n  this.word(\"interface\");\n  if (node.extends?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(node.extends);\n  }\n  this.space();\n  this.print(node.body);\n}\n\nexport function IntersectionTypeAnnotation(\n  this: Printer,\n  node: t.IntersectionTypeAnnotation,\n) {\n  this.printJoin(node.types, undefined, undefined, andSeparator);\n}\n\nexport function MixedTypeAnnotation(this: Printer) {\n  this.word(\"mixed\");\n}\n\nexport function EmptyTypeAnnotation(this: Printer) {\n  this.word(\"empty\");\n}\n\nexport function NullableTypeAnnotation(\n  this: Printer,\n  node: t.NullableTypeAnnotation,\n) {\n  this.token(\"?\");\n  this.print(node.typeAnnotation);\n}\n\nexport {\n  NumericLiteral as NumberLiteralTypeAnnotation,\n  StringLiteral as StringLiteralTypeAnnotation,\n} from \"./types.ts\";\n\nexport function NumberTypeAnnotation(this: Printer) {\n  this.word(\"number\");\n}\n\nexport function StringTypeAnnotation(this: Printer) {\n  this.word(\"string\");\n}\n\nexport function ThisTypeAnnotation(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function TupleTypeAnnotation(\n  this: Printer,\n  node: t.TupleTypeAnnotation,\n) {\n  this.token(\"[\");\n  this.printList(node.types);\n  this.token(\"]\");\n}\n\nexport function TypeofTypeAnnotation(\n  this: Printer,\n  node: t.TypeofTypeAnnotation,\n) {\n  this.word(\"typeof\");\n  this.space();\n  this.print(node.argument);\n}\n\nexport function TypeAlias(\n  this: Printer,\n  node: t.TypeAlias | t.DeclareTypeAlias,\n) {\n  this.word(\"type\");\n  this.space();\n  this.print(node.id);\n  this.print(node.typeParameters);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right);\n  this.semicolon();\n}\n\nexport function TypeAnnotation(\n  this: Printer,\n  node: t.TypeAnnotation,\n  parent: t.Node,\n) {\n  this.token(\":\");\n  this.space();\n  if (parent.type === \"ArrowFunctionExpression\") {\n    this.tokenContext |= TokenContext.arrowFlowReturnType;\n  } else if (\n    // @ts-expect-error todo(flow->ts) can this be removed? `.optional` looks to be not existing property\n    node.optional\n  ) {\n    this.token(\"?\");\n  }\n  this.print(node.typeAnnotation);\n}\n\nexport function TypeParameterInstantiation(\n  this: Printer,\n  node: t.TypeParameterInstantiation,\n): void {\n  this.token(\"<\");\n  this.printList(node.params);\n  this.token(\">\");\n}\n\nexport { TypeParameterInstantiation as TypeParameterDeclaration };\n\nexport function TypeParameter(this: Printer, node: t.TypeParameter) {\n  this._variance(node);\n\n  this.word(node.name);\n\n  if (node.bound) {\n    this.print(node.bound);\n  }\n\n  if (node.default) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.default);\n  }\n}\n\nexport function OpaqueType(\n  this: Printer,\n  node: t.OpaqueType | t.DeclareOpaqueType,\n) {\n  this.word(\"opaque\");\n  this.space();\n  this.word(\"type\");\n  this.space();\n  this.print(node.id);\n  this.print(node.typeParameters);\n  if (node.supertype) {\n    this.token(\":\");\n    this.space();\n    this.print(node.supertype);\n  }\n\n  if (node.impltype) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.impltype);\n  }\n  this.semicolon();\n}\n\nexport function ObjectTypeAnnotation(\n  this: Printer,\n  node: t.ObjectTypeAnnotation,\n) {\n  if (node.exact) {\n    this.token(\"{|\");\n  } else {\n    this.token(\"{\");\n  }\n\n  // TODO: remove the array fallbacks and instead enforce the types to require an array\n  const props = [\n    ...node.properties,\n    ...(node.callProperties || []),\n    ...(node.indexers || []),\n    ...(node.internalSlots || []),\n  ];\n\n  if (props.length) {\n    this.newline();\n\n    this.space();\n\n    this.printJoin(\n      props,\n      true,\n      true,\n      undefined,\n      undefined,\n      function addNewlines(leading) {\n        if (leading && !props[0]) return 1;\n      },\n      () => {\n        if (props.length !== 1 || node.inexact) {\n          this.token(\",\");\n          this.space();\n        }\n      },\n    );\n\n    this.space();\n  }\n\n  if (node.inexact) {\n    this.indent();\n    this.token(\"...\");\n    if (props.length) {\n      this.newline();\n    }\n    this.dedent();\n  }\n\n  if (node.exact) {\n    this.token(\"|}\");\n  } else {\n    this.token(\"}\");\n  }\n}\n\nexport function ObjectTypeInternalSlot(\n  this: Printer,\n  node: t.ObjectTypeInternalSlot,\n) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.token(\"[\");\n  this.token(\"[\");\n  this.print(node.id);\n  this.token(\"]\");\n  this.token(\"]\");\n  if (node.optional) this.token(\"?\");\n  if (!node.method) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.value);\n}\n\nexport function ObjectTypeCallProperty(\n  this: Printer,\n  node: t.ObjectTypeCallProperty,\n) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.print(node.value);\n}\n\nexport function ObjectTypeIndexer(this: Printer, node: t.ObjectTypeIndexer) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this._variance(node);\n  this.token(\"[\");\n  if (node.id) {\n    this.print(node.id);\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.key);\n  this.token(\"]\");\n  this.token(\":\");\n  this.space();\n  this.print(node.value);\n}\n\nexport function ObjectTypeProperty(this: Printer, node: t.ObjectTypeProperty) {\n  if (node.proto) {\n    this.word(\"proto\");\n    this.space();\n  }\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (node.kind === \"get\" || node.kind === \"set\") {\n    this.word(node.kind);\n    this.space();\n  }\n  this._variance(node);\n  this.print(node.key);\n  if (node.optional) this.token(\"?\");\n  if (!node.method) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.value);\n}\n\nexport function ObjectTypeSpreadProperty(\n  this: Printer,\n  node: t.ObjectTypeSpreadProperty,\n) {\n  this.token(\"...\");\n  this.print(node.argument);\n}\n\nexport function QualifiedTypeIdentifier(\n  this: Printer,\n  node: t.QualifiedTypeIdentifier,\n) {\n  this.print(node.qualification);\n  this.token(\".\");\n  this.print(node.id);\n}\n\nexport function SymbolTypeAnnotation(this: Printer) {\n  this.word(\"symbol\");\n}\n\nfunction orSeparator(this: Printer, occurrenceCount: number) {\n  this.space();\n  this.token(\"|\", false, occurrenceCount);\n  this.space();\n}\n\nexport function UnionTypeAnnotation(\n  this: Printer,\n  node: t.UnionTypeAnnotation,\n) {\n  this.printJoin(node.types, undefined, undefined, orSeparator);\n}\n\nexport function TypeCastExpression(this: Printer, node: t.TypeCastExpression) {\n  this.token(\"(\");\n  this.print(node.expression);\n  this.print(node.typeAnnotation);\n  this.token(\")\");\n}\n\nexport function Variance(this: Printer, node: t.Variance) {\n  if (node.kind === \"plus\") {\n    this.token(\"+\");\n  } else {\n    this.token(\"-\");\n  }\n}\n\nexport function VoidTypeAnnotation(this: Printer) {\n  this.word(\"void\");\n}\n\nexport function IndexedAccessType(this: Printer, node: t.IndexedAccessType) {\n  this.print(node.objectType, true);\n  this.token(\"[\");\n  this.print(node.indexType);\n  this.token(\"]\");\n}\n\nexport function OptionalIndexedAccessType(\n  this: Printer,\n  node: t.OptionalIndexedAccessType,\n) {\n  this.print(node.objectType);\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n  this.token(\"[\");\n  this.print(node.indexType);\n  this.token(\"]\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAqdA,IAAAG,OAAA,GAAAH,OAAA;AAGoB;EA3dXI,0BAA0B;EAAEC;AAAW,IAAAN,EAAA;AAKzC,SAASO,iBAAiBA,CAAA,EAAgB;EAC/C,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;AAClB;AAEO,SAASC,mBAAmBA,CAEjCC,IAA2B,EAC3B;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,WAAW,EAAE,IAAI,CAAC;EAClC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB;AAEO,SAASC,qBAAqBA,CAAA,EAAgB;EACnD,IAAI,CAACN,IAAI,CAAC,SAAS,CAAC;AACtB;AAEO,SAASO,4BAA4BA,CAE1CL,IAAoC,EACpC;EACA,IAAI,CAACF,IAAI,CAACE,IAAI,CAACM,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;AAC1C;AAEO,SAASC,yBAAyBA,CAAA,EAAgB;EACvD,IAAI,CAACT,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASU,YAAYA,CAE1BR,IAAoB,EACpBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACZ,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,aAAa,CAACX,IAAI,CAAC;AAC1B;AAEO,SAASY,eAAeA,CAE7BZ,IAAuB,EACvBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACZ,IAAI,CAAC,UAAU,CAAC;EACrB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EAEnB,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACa,EAAE,CAACC,cAAc,CAACA,cAAc,CAAC;EAEjD,IAAId,IAAI,CAACe,SAAS,EAAE;IAClB,IAAI,CAACL,KAAK,CAAC,CAAC;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACe,SAAS,CAAC;EAC5B;EAEA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASC,iBAAiBA,CAAA,EAAgB;EAC/C,IAAI,CAACd,SAAK,GAAI,CAAC;EACf,IAAI,CAACL,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASoB,iBAAiBA,CAAgBlB,IAAyB,EAAE;EAC1E,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACL,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACK,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACM,KAAK,CAAC;EACtB,IAAI,CAACH,SAAK,GAAI,CAAC;AACjB;AAEO,SAASgB,gBAAgBA,CAAgBnB,IAAwB,EAAE;EACxE,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACU,oBAAoB,CAACpB,IAAI,CAAC;AACjC;AAEO,SAASqB,aAAaA,CAAgBrB,IAAqB,EAAE;EAClE,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACnB,IAAI,CAACH,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACsB,IAAI,CAAC;AACvB;AAEO,SAASC,oBAAoBA,CAElCvB,IAA4B,EAC5B;EACA,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACK,SAAK,GAAI,CAAC;EACf,IAAI,CAACL,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACG,KAAK,CAACD,IAAI,CAACc,cAAc,CAAC;AACjC;AAEO,SAASU,gBAAgBA,CAAgBxB,IAAwB,EAAE;EACxE,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACe,SAAS,CAACzB,IAAI,CAAC;AACtB;AAEO,SAAS0B,iBAAiBA,CAE/B1B,IAAyB,EACzBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACiB,UAAU,CAAC3B,IAAI,CAAC;AACvB;AAEO,SAAS4B,eAAeA,CAE7B5B,IAAuB,EACvBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACZ,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACnB,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACa,EAAE,CAACC,cAAc,CAAC;EAClC,IAAI,CAACE,SAAS,CAAC,CAAC;AAClB;AAEO,SAASa,wBAAwBA,CAEtC7B,IAAgC,EAChC;EACA,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAIV,IAAI,CAAC8B,OAAO,EAAE;IAChB,IAAI,CAAChC,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EAEAqB,qBAAqB,CAACC,IAAI,CAAC,IAAI,EAAEhC,IAAI,CAAC;AACxC;AAEO,SAASiC,2BAA2BA,CAEzCjC,IAAmC,EACnC;EACA,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZwB,6BAAoB,CAACF,IAAI,CAAC,IAAI,EAAEhC,IAAI,CAAC;AACvC;AAEO,SAASmC,eAAeA,CAAgBnC,IAAuB,EAAE;EACtE,MAAM;IAAEa,EAAE;IAAES;EAAK,CAAC,GAAGtB,IAAI;EACzB,IAAI,CAACF,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACY,EAAE,CAAC;EACd,IAAI,CAACZ,KAAK,CAACqB,IAAI,CAAC;AAClB;AAEA,SAASc,gBAAgBA,CACvBC,OAAgB,EAChBC,IAAY,EACZC,eAAwB,EACxB;EACA,IAAIA,eAAe,EAAE;IACnBF,OAAO,CAAC3B,KAAK,CAAC,CAAC;IACf2B,OAAO,CAACvC,IAAI,CAAC,IAAI,CAAC;IAClBuC,OAAO,CAAC3B,KAAK,CAAC,CAAC;IACf2B,OAAO,CAACvC,IAAI,CAACwC,IAAI,CAAC;EACpB;EACAD,OAAO,CAAC3B,KAAK,CAAC,CAAC;AACjB;AAEA,SAAS8B,QAAQA,CAACH,OAAgB,EAAErC,IAAgB,EAAE;EACpD,MAAM;IAAEyC;EAAQ,CAAC,GAAGzC,IAAI;EACxBqC,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;EAClBkC,OAAO,CAACK,MAAM,CAAC,CAAC;EAChBL,OAAO,CAACM,OAAO,CAAC,CAAC;EACjB,KAAK,MAAMC,MAAM,IAAIH,OAAO,EAAE;IAC5BJ,OAAO,CAACpC,KAAK,CAAC2C,MAAM,CAAC;IACrBP,OAAO,CAACM,OAAO,CAAC,CAAC;EACnB;EACA,IAAI3C,IAAI,CAAC6C,iBAAiB,EAAE;IAC1BR,OAAO,CAAClC,KAAK,CAAC,KAAK,CAAC;IACpBkC,OAAO,CAACM,OAAO,CAAC,CAAC;EACnB;EACAN,OAAO,CAACS,MAAM,CAAC,CAAC;EAChBT,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;AACpB;AAEO,SAAS4C,eAAeA,CAAgB/C,IAAuB,EAAE;EACtE,MAAM;IAAEgD;EAAa,CAAC,GAAGhD,IAAI;EAC7BoC,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAEY,YAAY,CAAC;EAC/CR,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASiD,cAAcA,CAAgBjD,IAAsB,EAAE;EACpE,MAAM;IAAEgD;EAAa,CAAC,GAAGhD,IAAI;EAC7BoC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAEY,YAAY,CAAC;EAC9CR,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASkD,cAAcA,CAAgBlD,IAAsB,EAAE;EACpE,MAAM;IAAEgD;EAAa,CAAC,GAAGhD,IAAI;EAC7BoC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAEY,YAAY,CAAC;EAC9CR,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASmD,cAAcA,CAAgBnD,IAAsB,EAAE;EACpEoC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC;EACtCI,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASoD,mBAAmBA,CAEjCpD,IAA2B,EAC3B;EACA,MAAM;IAAEa;EAAG,CAAC,GAAGb,IAAI;EACnB,IAAI,CAACC,KAAK,CAACY,EAAE,CAAC;EACd,IAAI,CAACV,SAAK,GAAI,CAAC;AACjB;AAEA,SAASkD,qBAAqBA,CAC5BhB,OAAgB,EAChBrC,IAAmE,EACnE;EACAqC,OAAO,CAACpC,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACtBwB,OAAO,CAAC3B,KAAK,CAAC,CAAC;EACf2B,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;EAClBkC,OAAO,CAAC3B,KAAK,CAAC,CAAC;EACf2B,OAAO,CAACpC,KAAK,CAACD,IAAI,CAACsD,IAAI,CAAC;EACxBjB,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;AACpB;AAEO,SAASoD,iBAAiBA,CAAgBvD,IAAyB,EAAE;EAC1EqD,qBAAqB,CAAC,IAAI,EAAErD,IAAI,CAAC;AACnC;AAEO,SAASwD,gBAAgBA,CAAgBxD,IAAwB,EAAE;EACxEqD,qBAAqB,CAAC,IAAI,EAAErD,IAAI,CAAC;AACnC;AAEO,SAASyD,gBAAgBA,CAAgBzD,IAAwB,EAAE;EACxEqD,qBAAqB,CAAC,IAAI,EAAErD,IAAI,CAAC;AACnC;AAEA,SAAS+B,qBAAqBA,CAE5B/B,IAAgC,EAChC;EACA,IAAIA,IAAI,CAAC0D,WAAW,EAAE;IACpB,MAAMC,MAAM,GAAG3D,IAAI,CAAC0D,WAAW;IAC/B,IAAI,CAACzD,KAAK,CAAC0D,MAAM,CAAC;IAClB,IAAI,CAAC/D,WAAW,CAAC+D,MAAM,CAAC,EAAE,IAAI,CAAC3C,SAAS,CAAC,CAAC;EAC5C,CAAC,MAAM;IACL,IAAI,CAACb,SAAK,IAAI,CAAC;IACf,IAAIH,IAAI,CAAC4D,UAAU,CAACC,MAAM,EAAE;MAC1B,IAAI,CAACnD,KAAK,CAAC,CAAC;MACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC4D,UAAU,CAAC;MAC/B,IAAI,CAAClD,KAAK,CAAC,CAAC;IACd;IACA,IAAI,CAACP,SAAK,IAAI,CAAC;IAEf,IAAIH,IAAI,CAAC+D,MAAM,EAAE;MACf,IAAI,CAACrD,KAAK,CAAC,CAAC;MACZ,IAAI,CAACZ,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACY,KAAK,CAAC,CAAC;MACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAAC+D,MAAM,CAAC;IACzB;IAEA,IAAI,CAAC/C,SAAS,CAAC,CAAC;EAClB;AACF;AAEO,SAASgD,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAAC7D,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS8D,sBAAsBA,CAEpCjE,IAA8B,EAC9BS,MAAe,EACf;EACA,IAAI,CAACR,KAAK,CAACD,IAAI,CAACkE,cAAc,CAAC;EAC/B,IAAI,CAAC/D,SAAK,GAAI,CAAC;EAEf,IAAIH,IAAI,CAACmE,IAAI,EAAE;IACb,IAAI,CAACrE,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACK,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACmE,IAAI,CAACrD,cAAc,CAAC;IACpC,IAAId,IAAI,CAACoE,MAAM,CAACP,MAAM,IAAI7D,IAAI,CAACqE,IAAI,EAAE;MACnC,IAAI,CAAClE,SAAK,GAAI,CAAC;MACf,IAAI,CAACO,KAAK,CAAC,CAAC;IACd;EACF;EAEA,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAACoE,MAAM,CAAC;EAE3B,IAAIpE,IAAI,CAACqE,IAAI,EAAE;IACb,IAAIrE,IAAI,CAACoE,MAAM,CAACP,MAAM,EAAE;MACtB,IAAI,CAAC1D,SAAK,GAAI,CAAC;MACf,IAAI,CAACO,KAAK,CAAC,CAAC;IACd;IACA,IAAI,CAACP,KAAK,CAAC,KAAK,CAAC;IACjB,IAAI,CAACF,KAAK,CAACD,IAAI,CAACqE,IAAI,CAAC;EACvB;EAEA,IAAI,CAAClE,SAAK,GAAI,CAAC;EAIf,MAAMmE,IAAI,GAAG7D,MAAM,oBAANA,MAAM,CAAE6D,IAAI;EACzB,IACEA,IAAI,IAAI,IAAI,KACXA,IAAI,KAAK,wBAAwB,IAChCA,IAAI,KAAK,wBAAwB,IACjCA,IAAI,KAAK,iBAAiB,IACzBA,IAAI,KAAK,oBAAoB,IAAI7D,MAAM,CAAC8D,MAAO,CAAC,EACnD;IACA,IAAI,CAACpE,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ,IAAI,CAACP,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAI,CAACO,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACwE,UAAU,CAAC;AAC7B;AAEO,SAASC,iBAAiBA,CAAgBzE,IAAyB,EAAE;EAC1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAACsC,IAAI,CAAC;EACrB,IAAItC,IAAI,CAAC0E,QAAQ,EAAE,IAAI,CAACvE,SAAK,GAAI,CAAC;EAClC,IAAIH,IAAI,CAACsC,IAAI,EAAE;IACb,IAAI,CAACnC,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACc,cAAc,CAAC;AACjC;AAEO,SAAS6D,gBAAgBA,CAAgB3E,IAAwB,EAAE;EACxE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACnB,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACkE,cAAc,EAAE,IAAI,CAAC;AACvC;AAOO,SAASvD,aAAaA,CAE3BX,IAAkE,EAClE;EAAA,IAAA4E,aAAA;EACA,IAAI,CAAC3E,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACnB,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACkE,cAAc,CAAC;EAC/B,KAAAU,aAAA,GAAI5E,IAAI,CAAC6E,OAAO,aAAZD,aAAA,CAAcf,MAAM,EAAE;IACxB,IAAI,CAACnD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,CAAC,CAAC;IACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC6E,OAAO,CAAC;EAC9B;EACA,IAAI7E,IAAI,CAACsE,IAAI,KAAK,cAAc,EAAE;IAAA,IAAAQ,YAAA,EAAAC,gBAAA;IAChC,KAAAD,YAAA,GAAI9E,IAAI,CAACgF,MAAM,aAAXF,YAAA,CAAajB,MAAM,EAAE;MACvB,IAAI,CAACnD,KAAK,CAAC,CAAC;MACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;MACnB,IAAI,CAACY,KAAK,CAAC,CAAC;MACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAACgF,MAAM,CAAC;IAC7B;IACA,KAAAD,gBAAA,GAAI/E,IAAI,CAACiF,UAAU,aAAfF,gBAAA,CAAiBlB,MAAM,EAAE;MAC3B,IAAI,CAACnD,KAAK,CAAC,CAAC;MACZ,IAAI,CAACZ,IAAI,CAAC,YAAY,CAAC;MACvB,IAAI,CAACY,KAAK,CAAC,CAAC;MACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAACiF,UAAU,CAAC;IACjC;EACF;EACA,IAAI,CAACvE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACsB,IAAI,CAAC;AACvB;AAEO,SAAS4D,SAASA,CAEvBlF,IAM2B,EAC3B;EAAA,IAAAmF,cAAA;EACA,MAAMC,IAAI,IAAAD,cAAA,GAAGnF,IAAI,CAACqF,QAAQ,qBAAbF,cAAA,CAAeC,IAAI;EAChC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,IAAIA,IAAI,KAAK,MAAM,EAAE;MACnB,IAAI,CAACjF,SAAK,GAAI,CAAC;IACjB,CAAC,MAAM,IAAIiF,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACjF,SAAK,GAAI,CAAC;IACjB;EACF;AACF;AAEO,SAASiB,oBAAoBA,CAElCpB,IAAiD,EACjD;EACA,IAAI,CAACF,IAAI,CAAC,WAAW,CAAC;EACtB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,aAAa,CAACX,IAAI,CAAC;AAC1B;AAEA,SAASsF,YAAYA,CAAgBC,eAAuB,EAAE;EAC5D,IAAI,CAAC7E,KAAK,CAAC,CAAC;EACZ,IAAI,CAACP,KAAK,CAAC,GAAG,EAAE,KAAK,EAAEoF,eAAe,CAAC;EACvC,IAAI,CAAC7E,KAAK,CAAC,CAAC;AACd;AAEO,SAAS8E,uBAAuBA,CAErCxF,IAA+B,EAC/B;EAAA,IAAAyF,cAAA;EACA,IAAI,CAAC3F,IAAI,CAAC,WAAW,CAAC;EACtB,KAAA2F,cAAA,GAAIzF,IAAI,CAAC6E,OAAO,aAAZY,cAAA,CAAc5B,MAAM,EAAE;IACxB,IAAI,CAACnD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,CAAC,CAAC;IACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC6E,OAAO,CAAC;EAC9B;EACA,IAAI,CAACnE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACsB,IAAI,CAAC;AACvB;AAEO,SAASoE,0BAA0BA,CAExC1F,IAAkC,EAClC;EACA,IAAI,CAAC2F,SAAS,CAAC3F,IAAI,CAAC4F,KAAK,EAAEC,SAAS,EAAEA,SAAS,EAAEP,YAAY,CAAC;AAChE;AAEO,SAASQ,mBAAmBA,CAAA,EAAgB;EACjD,IAAI,CAAChG,IAAI,CAAC,OAAO,CAAC;AACpB;AAEO,SAASiG,mBAAmBA,CAAA,EAAgB;EACjD,IAAI,CAACjG,IAAI,CAAC,OAAO,CAAC;AACpB;AAEO,SAASkG,sBAAsBA,CAEpChG,IAA8B,EAC9B;EACA,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACc,cAAc,CAAC;AACjC;AAOO,SAASmF,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAACnG,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASoG,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAACpG,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASqG,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAACrG,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASsG,mBAAmBA,CAEjCpG,IAA2B,EAC3B;EACA,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAAC2D,SAAS,CAAC9D,IAAI,CAAC4F,KAAK,CAAC;EAC1B,IAAI,CAACzF,SAAK,GAAI,CAAC;AACjB;AAEO,SAASkG,oBAAoBA,CAElCrG,IAA4B,EAC5B;EACA,IAAI,CAACF,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACsG,QAAQ,CAAC;AAC3B;AAEO,SAAS7E,SAASA,CAEvBzB,IAAsC,EACtC;EACA,IAAI,CAACF,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACnB,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACkE,cAAc,CAAC;EAC/B,IAAI,CAACxD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACP,SAAK,GAAI,CAAC;EACf,IAAI,CAACO,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACuG,KAAK,CAAC;EACtB,IAAI,CAACvF,SAAS,CAAC,CAAC;AAClB;AAEO,SAASwF,cAAcA,CAE5BxG,IAAsB,EACtBS,MAAc,EACd;EACA,IAAI,CAACN,SAAK,GAAI,CAAC;EACf,IAAI,CAACO,KAAK,CAAC,CAAC;EACZ,IAAID,MAAM,CAAC6D,IAAI,KAAK,yBAAyB,EAAE;IAC7C,IAAI,CAACmC,YAAY,IAAIC,mBAAY,CAACC,mBAAmB;EACvD,CAAC,MAAM,IAEL3G,IAAI,CAAC0E,QAAQ,EACb;IACA,IAAI,CAACvE,SAAK,GAAI,CAAC;EACjB;EACA,IAAI,CAACF,KAAK,CAACD,IAAI,CAACc,cAAc,CAAC;AACjC;AAEO,SAAS8F,0BAA0BA,CAExC5G,IAAkC,EAC5B;EACN,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAAC2D,SAAS,CAAC9D,IAAI,CAACoE,MAAM,CAAC;EAC3B,IAAI,CAACjE,SAAK,GAAI,CAAC;AACjB;AAIO,SAAS0G,aAAaA,CAAgB7G,IAAqB,EAAE;EAClE,IAAI,CAACkF,SAAS,CAAClF,IAAI,CAAC;EAEpB,IAAI,CAACF,IAAI,CAACE,IAAI,CAACsC,IAAI,CAAC;EAEpB,IAAItC,IAAI,CAAC8G,KAAK,EAAE;IACd,IAAI,CAAC7G,KAAK,CAACD,IAAI,CAAC8G,KAAK,CAAC;EACxB;EAEA,IAAI9G,IAAI,CAAC8B,OAAO,EAAE;IAChB,IAAI,CAACpB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACP,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAAC8B,OAAO,CAAC;EAC1B;AACF;AAEO,SAASH,UAAUA,CAExB3B,IAAwC,EACxC;EACA,IAAI,CAACF,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACZ,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACY,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACnB,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACkE,cAAc,CAAC;EAC/B,IAAIlE,IAAI,CAAC+G,SAAS,EAAE;IAClB,IAAI,CAAC5G,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAAC+G,SAAS,CAAC;EAC5B;EAEA,IAAI/G,IAAI,CAACgH,QAAQ,EAAE;IACjB,IAAI,CAACtG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACP,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACgH,QAAQ,CAAC;EAC3B;EACA,IAAI,CAAChG,SAAS,CAAC,CAAC;AAClB;AAEO,SAASiG,oBAAoBA,CAElCjH,IAA4B,EAC5B;EACA,IAAIA,IAAI,CAACkH,KAAK,EAAE;IACd,IAAI,CAAC/G,KAAK,CAAC,IAAI,CAAC;EAClB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,IAAI,CAAC;EACjB;EAGA,MAAMgH,KAAK,GAAG,CACZ,GAAGnH,IAAI,CAACoH,UAAU,EAClB,IAAIpH,IAAI,CAACqH,cAAc,IAAI,EAAE,CAAC,EAC9B,IAAIrH,IAAI,CAACsH,QAAQ,IAAI,EAAE,CAAC,EACxB,IAAItH,IAAI,CAACuH,aAAa,IAAI,EAAE,CAAC,CAC9B;EAED,IAAIJ,KAAK,CAACtD,MAAM,EAAE;IAChB,IAAI,CAAClB,OAAO,CAAC,CAAC;IAEd,IAAI,CAACjC,KAAK,CAAC,CAAC;IAEZ,IAAI,CAACiF,SAAS,CACZwB,KAAK,EACL,IAAI,EACJ,IAAI,EACJtB,SAAS,EACTA,SAAS,EACT,SAAS2B,WAAWA,CAACC,OAAO,EAAE;MAC5B,IAAIA,OAAO,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACpC,CAAC,EACD,MAAM;MACJ,IAAIA,KAAK,CAACtD,MAAM,KAAK,CAAC,IAAI7D,IAAI,CAAC0H,OAAO,EAAE;QACtC,IAAI,CAACvH,SAAK,GAAI,CAAC;QACf,IAAI,CAACO,KAAK,CAAC,CAAC;MACd;IACF,CACF,CAAC;IAED,IAAI,CAACA,KAAK,CAAC,CAAC;EACd;EAEA,IAAIV,IAAI,CAAC0H,OAAO,EAAE;IAChB,IAAI,CAAChF,MAAM,CAAC,CAAC;IACb,IAAI,CAACvC,KAAK,CAAC,KAAK,CAAC;IACjB,IAAIgH,KAAK,CAACtD,MAAM,EAAE;MAChB,IAAI,CAAClB,OAAO,CAAC,CAAC;IAChB;IACA,IAAI,CAACG,MAAM,CAAC,CAAC;EACf;EAEA,IAAI9C,IAAI,CAACkH,KAAK,EAAE;IACd,IAAI,CAAC/G,KAAK,CAAC,IAAI,CAAC;EAClB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,IAAI,CAAC;EACjB;AACF;AAEO,SAASwH,sBAAsBA,CAEpC3H,IAA8B,EAC9B;EACA,IAAIA,IAAI,CAAC4H,MAAM,EAAE;IACf,IAAI,CAAC9H,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACP,SAAK,GAAI,CAAC;EACf,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;EACnB,IAAI,CAACV,SAAK,GAAI,CAAC;EACf,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAIH,IAAI,CAAC0E,QAAQ,EAAE,IAAI,CAACvE,SAAK,GAAI,CAAC;EAClC,IAAI,CAACH,IAAI,CAACuE,MAAM,EAAE;IAChB,IAAI,CAACpE,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,CAAC;AACxB;AAEO,SAASuH,sBAAsBA,CAEpC7H,IAA8B,EAC9B;EACA,IAAIA,IAAI,CAAC4H,MAAM,EAAE;IACf,IAAI,CAAC9H,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,CAAC;AACxB;AAEO,SAASwH,iBAAiBA,CAAgB9H,IAAyB,EAAE;EAC1E,IAAIA,IAAI,CAAC4H,MAAM,EAAE;IACf,IAAI,CAAC9H,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACwE,SAAS,CAAClF,IAAI,CAAC;EACpB,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAIH,IAAI,CAACa,EAAE,EAAE;IACX,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;IACnB,IAAI,CAACV,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAAC+H,GAAG,CAAC;EACpB,IAAI,CAAC5H,SAAK,GAAI,CAAC;EACf,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAI,CAACO,KAAK,CAAC,CAAC;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,CAAC;AACxB;AAEO,SAAS0H,kBAAkBA,CAAgBhI,IAA0B,EAAE;EAC5E,IAAIA,IAAI,CAACiI,KAAK,EAAE;IACd,IAAI,CAACnI,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAIV,IAAI,CAAC4H,MAAM,EAAE;IACf,IAAI,CAAC9H,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,CAAC,CAAC;EACd;EACA,IAAIV,IAAI,CAACoF,IAAI,KAAK,KAAK,IAAIpF,IAAI,CAACoF,IAAI,KAAK,KAAK,EAAE;IAC9C,IAAI,CAACtF,IAAI,CAACE,IAAI,CAACoF,IAAI,CAAC;IACpB,IAAI,CAAC1E,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACwE,SAAS,CAAClF,IAAI,CAAC;EACpB,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC+H,GAAG,CAAC;EACpB,IAAI/H,IAAI,CAAC0E,QAAQ,EAAE,IAAI,CAACvE,SAAK,GAAI,CAAC;EAClC,IAAI,CAACH,IAAI,CAACuE,MAAM,EAAE;IAChB,IAAI,CAACpE,SAAK,GAAI,CAAC;IACf,IAAI,CAACO,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,CAAC;AACxB;AAEO,SAAS4H,wBAAwBA,CAEtClI,IAAgC,EAChC;EACA,IAAI,CAACG,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACF,KAAK,CAACD,IAAI,CAACsG,QAAQ,CAAC;AAC3B;AAEO,SAAS6B,uBAAuBA,CAErCnI,IAA+B,EAC/B;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACoI,aAAa,CAAC;EAC9B,IAAI,CAACjI,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACa,EAAE,CAAC;AACrB;AAEO,SAASwH,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAACvI,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEA,SAASwI,WAAWA,CAAgB/C,eAAuB,EAAE;EAC3D,IAAI,CAAC7E,KAAK,CAAC,CAAC;EACZ,IAAI,CAACP,KAAK,CAAC,GAAG,EAAE,KAAK,EAAEoF,eAAe,CAAC;EACvC,IAAI,CAAC7E,KAAK,CAAC,CAAC;AACd;AAEO,SAAS6H,mBAAmBA,CAEjCvI,IAA2B,EAC3B;EACA,IAAI,CAAC2F,SAAS,CAAC3F,IAAI,CAAC4F,KAAK,EAAEC,SAAS,EAAEA,SAAS,EAAEyC,WAAW,CAAC;AAC/D;AAEO,SAASE,kBAAkBA,CAAgBxI,IAA0B,EAAE;EAC5E,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACyI,UAAU,CAAC;EAC3B,IAAI,CAACxI,KAAK,CAACD,IAAI,CAACc,cAAc,CAAC;EAC/B,IAAI,CAACX,SAAK,GAAI,CAAC;AACjB;AAEO,SAASuI,QAAQA,CAAgB1I,IAAgB,EAAE;EACxD,IAAIA,IAAI,CAACoF,IAAI,KAAK,MAAM,EAAE;IACxB,IAAI,CAACjF,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,GAAI,CAAC;EACjB;AACF;AAEO,SAASwI,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAAC7I,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAAS8I,iBAAiBA,CAAgB5I,IAAyB,EAAE;EAC1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC6I,UAAU,EAAE,IAAI,CAAC;EACjC,IAAI,CAAC1I,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAAC8I,SAAS,CAAC;EAC1B,IAAI,CAAC3I,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS4I,yBAAyBA,CAEvC/I,IAAiC,EACjC;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC6I,UAAU,CAAC;EAC3B,IAAI7I,IAAI,CAAC0E,QAAQ,EAAE;IACjB,IAAI,CAACvE,KAAK,CAAC,IAAI,CAAC;EAClB;EACA,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAAC8I,SAAS,CAAC;EAC1B,IAAI,CAAC3I,SAAK,GAAI,CAAC;AACjB", "ignoreList": []}