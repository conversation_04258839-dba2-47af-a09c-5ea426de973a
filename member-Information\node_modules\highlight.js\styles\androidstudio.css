pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*
Date: 24 Fev 2015
Author: <PERSON> <kanytu@gmail . com>
*/
.hljs {
  color: #a9b7c6;
  background: #282b2e
}
.hljs-number,
.hljs-literal,
.hljs-symbol,
.hljs-bullet {
  color: #6897BB
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-deletion {
  color: #cc7832
}
.hljs-variable,
.hljs-template-variable,
.hljs-link {
  color: #629755
}
.hljs-comment,
.hljs-quote {
  color: #808080
}
.hljs-meta {
  color: #bbb529
}
.hljs-string,
.hljs-attribute,
.hljs-addition {
  color: #6A8759
}
.hljs-section,
.hljs-title,
.hljs-type {
  color: #ffc66d
}
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #e8bf6a
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}