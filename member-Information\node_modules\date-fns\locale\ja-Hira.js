"use strict";
exports.jaHira = void 0;
var _index = require("./ja-Hira/_lib/formatDistance.js");
var _index2 = require("./ja-Hira/_lib/formatLong.js");
var _index3 = require("./ja-Hira/_lib/formatRelative.js");
var _index4 = require("./ja-Hira/_lib/localize.js");
var _index5 = require("./ja-Hira/_lib/match.js");

/**
 * @category Locales
 * @summary Japanese (Hiragana) locale.
 * @language Japanese (Hiragana)
 * @iso-639-2 jpn
 * <AUTHOR> [@Eritutteo](https://github.com/Eritutteo)
 */
const jaHira = (exports.jaHira = {
  code: "ja-Hira",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
