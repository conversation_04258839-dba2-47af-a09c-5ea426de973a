/**
* @vue/reactivity v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/var VueReactivity=function(e){"use strict";let t,i,s,r,n,l={},o=()=>{},a=Object.assign,u=Object.prototype.hasOwnProperty,c=(e,t)=>u.call(e,t),f=Array.isArray,h=e=>"[object Map]"===v(e),p=e=>"symbol"==typeof e,d=e=>null!==e&&"object"==typeof e,_=Object.prototype.toString,v=e=>_.call(e),g=e=>"string"==typeof e&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,y=(e,t)=>!Object.is(e,t);class R{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=t,!e&&t&&(this.index=(t.scopes||(t.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){let i=t;try{return t=this,e()}finally{t=i}}}on(){1==++this._on&&(this.prevScope=t,t=this)}off(){this._on>0&&0==--this._on&&(t=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,i;for(t=0,this._active=!1,i=this.effects.length;t<i;t++)this.effects[t].stop();for(t=0,this.effects.length=0,i=this.cleanups.length;t<i;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}let b=new WeakSet;class w{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,t&&t.active&&t.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,b.has(this)&&(b.delete(this),this.trigger()))}notify(){(!(2&this.flags)||32&this.flags)&&(8&this.flags||E(this))}run(){if(!(1&this.flags))return this.fn();this.flags|=2,C(this),T(this);let e=i,t=O;i=this,O=!0;try{return this.fn()}finally{m(this),i=e,O=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)D(e);this.deps=this.depsTail=void 0,C(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?b.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){A(this)&&this.run()}get dirty(){return A(this)}}let S=0;function E(e,t=!1){if(e.flags|=8,t){e.next=r,r=e;return}e.next=s,s=e}function x(){let e;if(!(--S>0)){if(r){let e=r;for(r=void 0;e;){let t=e.next;e.next=void 0,e.flags&=-9,e=t}}for(;s;){let t=s;for(s=void 0;t;){let i=t.next;if(t.next=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=i}}if(e)throw e}}function T(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function m(e){let t,i=e.depsTail,s=i;for(;s;){let e=s.prevDep;-1===s.version?(s===i&&(i=e),D(s),function(e){let{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=e}e.deps=t,e.depsTail=i}function A(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(k(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function k(e){if(4&e.flags&&!(16&e.flags)||(e.flags&=-17,e.globalVersion===P)||(e.globalVersion=P,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!A(e))))return;e.flags|=2;let t=e.dep,s=i,r=O;i=e,O=!0;try{T(e);let i=e.fn(e._value);(0===t.version||y(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(e){throw t.version++,e}finally{i=s,O=r,m(e),e.flags&=-3}}function D(e,t=!1){let{dep:i,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),i.subs===e&&(i.subs=s,!s&&i.computed)){i.computed.flags&=-5;for(let e=i.computed.deps;e;e=e.nextDep)D(e,!0)}t||--i.sc||!i.map||i.map.delete(i.key)}let O=!0,I=[];function L(){I.push(O),O=!1}function j(){let e=I.pop();O=void 0===e||e}function C(e){let{cleanup:t}=e;if(e.cleanup=void 0,t){let e=i;i=void 0;try{t()}finally{i=e}}}let P=0;class W{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class N{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!i||!O||i===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==i)t=this.activeLink=new W(i,this),i.deps?(t.prevDep=i.depsTail,i.depsTail.nextDep=t,i.depsTail=t):i.deps=i.depsTail=t,function e(t){if(t.dep.sc++,4&t.sub.flags){let i=t.dep.computed;if(i&&!t.dep.subs){i.flags|=20;for(let t=i.deps;t;t=t.nextDep)e(t)}let s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){let e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=i.depsTail,t.nextDep=void 0,i.depsTail.nextDep=t,i.depsTail=t,i.deps===t&&(i.deps=e)}return t}trigger(e){this.version++,P++,this.notify(e)}notify(e){S++;try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{x()}}}let V=new WeakMap,K=Symbol(""),M=Symbol(""),U=Symbol("");function H(e,t,s){if(O&&i){let t=V.get(e);t||V.set(e,t=new Map);let i=t.get(s);i||(t.set(s,i=new N),i.map=t,i.key=s),i.track()}}function Y(e,t,i,s,r,n){let l=V.get(e);if(!l)return void P++;let o=e=>{e&&e.trigger()};if(S++,"clear"===t)l.forEach(o);else{let r=f(e),n=r&&g(i);if(r&&"length"===i){let e=Number(s);l.forEach((t,i)=>{("length"===i||i===U||!p(i)&&i>=e)&&o(t)})}else switch((void 0!==i||l.has(void 0))&&o(l.get(i)),n&&o(l.get(U)),t){case"add":r?n&&o(l.get("length")):(o(l.get(K)),h(e)&&o(l.get(M)));break;case"delete":!r&&(o(l.get(K)),h(e)&&o(l.get(M)));break;case"set":h(e)&&o(l.get(K))}}x()}function G(e){let t=ek(e);return t===e?t:(H(t,"iterate",U),em(e)?t:t.map(eD))}function F(e){return H(e=ek(e),"iterate",U),e}let z={__proto__:null,[Symbol.iterator](){return B(this,Symbol.iterator,eD)},concat(...e){return G(this).concat(...e.map(e=>f(e)?G(e):e))},entries(){return B(this,"entries",e=>(e[1]=eD(e[1]),e))},every(e,t){return J(this,"every",e,t,void 0,arguments)},filter(e,t){return J(this,"filter",e,t,e=>e.map(eD),arguments)},find(e,t){return J(this,"find",e,t,eD,arguments)},findIndex(e,t){return J(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return J(this,"findLast",e,t,eD,arguments)},findLastIndex(e,t){return J(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return J(this,"forEach",e,t,void 0,arguments)},includes(...e){return X(this,"includes",e)},indexOf(...e){return X(this,"indexOf",e)},join(e){return G(this).join(e)},lastIndexOf(...e){return X(this,"lastIndexOf",e)},map(e,t){return J(this,"map",e,t,void 0,arguments)},pop(){return Z(this,"pop")},push(...e){return Z(this,"push",e)},reduce(e,...t){return Q(this,"reduce",e,t)},reduceRight(e,...t){return Q(this,"reduceRight",e,t)},shift(){return Z(this,"shift")},some(e,t){return J(this,"some",e,t,void 0,arguments)},splice(...e){return Z(this,"splice",e)},toReversed(){return G(this).toReversed()},toSorted(e){return G(this).toSorted(e)},toSpliced(...e){return G(this).toSpliced(...e)},unshift(...e){return Z(this,"unshift",e)},values(){return B(this,"values",eD)}};function B(e,t,i){let s=F(e),r=s[t]();return s===e||em(e)||(r._next=r.next,r.next=()=>{let e=r._next();return e.value&&(e.value=i(e.value)),e}),r}let q=Array.prototype;function J(e,t,i,s,r,n){let l=F(e),o=l!==e&&!em(e),a=l[t];if(a!==q[t]){let t=a.apply(e,n);return o?eD(t):t}let u=i;l!==e&&(o?u=function(t,s){return i.call(this,eD(t),s,e)}:i.length>2&&(u=function(t,s){return i.call(this,t,s,e)}));let c=a.call(l,u,s);return o&&r?r(c):c}function Q(e,t,i,s){let r=F(e),n=i;return r!==e&&(em(e)?i.length>3&&(n=function(t,s,r){return i.call(this,t,s,r,e)}):n=function(t,s,r){return i.call(this,t,eD(s),r,e)}),r[t](n,...s)}function X(e,t,i){let s=ek(e);H(s,"iterate",U);let r=s[t](...i);return(-1===r||!1===r)&&eA(i[0])?(i[0]=ek(i[0]),s[t](...i)):r}function Z(e,t,i=[]){L(),S++;let s=ek(e)[t].apply(e,i);return x(),j(),s}let $=function(e){let t=Object.create(null);for(let i of e.split(","))t[i]=1;return e=>e in t}("__proto__,__v_isRef,__isVue"),ee=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(p));function et(e){p(e)||(e=String(e));let t=ek(this);return H(t,"has",e),t.hasOwnProperty(e)}class ei{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,i){if("__v_skip"===t)return e.__v_skip;let s=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return r;if("__v_raw"===t)return i===(s?r?eb:eR:r?ey:eg).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(i)?e:void 0;let n=f(e);if(!s){let e;if(n&&(e=z[t]))return e;if("hasOwnProperty"===t)return et}let l=Reflect.get(e,t,eI(e)?e:i);return(p(t)?ee.has(t):$(t))||(s||H(e,"get",t),r)?l:eI(l)?n&&g(t)?l:l.value:d(l)?s?eS(l):ew(l):l}}class es extends ei{constructor(e=!1){super(!1,e)}set(e,t,i,s){let r=e[t];if(!this._isShallow){let t=eT(r);if(em(i)||eT(i)||(r=ek(r),i=ek(i)),!f(e)&&eI(r)&&!eI(i))if(t)return!1;else return r.value=i,!0}let n=f(e)&&g(t)?Number(t)<e.length:c(e,t),l=Reflect.set(e,t,i,eI(e)?e:s);return e===ek(s)&&(n?y(i,r)&&Y(e,"set",t,i):Y(e,"add",t,i)),l}deleteProperty(e,t){let i=c(e,t);e[t];let s=Reflect.deleteProperty(e,t);return s&&i&&Y(e,"delete",t,void 0),s}has(e,t){let i=Reflect.has(e,t);return p(t)&&ee.has(t)||H(e,"has",t),i}ownKeys(e){return H(e,"iterate",f(e)?"length":K),Reflect.ownKeys(e)}}class er extends ei{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}let en=new es,el=new er,eo=new es(!0),ea=new er(!0),eu=e=>e,ec=e=>Reflect.getPrototypeOf(e);function ef(e){return function(){return"delete"!==e&&("clear"===e?void 0:this)}}function eh(e,t){let i=function(e,t){let i={get(i){let s=this.__v_raw,r=ek(s),n=ek(i);e||(y(i,n)&&H(r,"get",i),H(r,"get",n));let{has:l}=ec(r),o=t?eu:e?eO:eD;return l.call(r,i)?o(s.get(i)):l.call(r,n)?o(s.get(n)):void(s!==r&&s.get(i))},get size(){let t=this.__v_raw;return e||H(ek(t),"iterate",K),Reflect.get(t,"size",t)},has(t){let i=this.__v_raw,s=ek(i),r=ek(t);return e||(y(t,r)&&H(s,"has",t),H(s,"has",r)),t===r?i.has(t):i.has(t)||i.has(r)},forEach(i,s){let r=this,n=r.__v_raw,l=ek(n),o=t?eu:e?eO:eD;return e||H(l,"iterate",K),n.forEach((e,t)=>i.call(s,o(e),o(t),r))}};return a(i,e?{add:ef("add"),set:ef("set"),delete:ef("delete"),clear:ef("clear")}:{add(e){t||em(e)||eT(e)||(e=ek(e));let i=ek(this);return ec(i).has.call(i,e)||(i.add(e),Y(i,"add",e,e)),this},set(e,i){t||em(i)||eT(i)||(i=ek(i));let s=ek(this),{has:r,get:n}=ec(s),l=r.call(s,e);l||(e=ek(e),l=r.call(s,e));let o=n.call(s,e);return s.set(e,i),l?y(i,o)&&Y(s,"set",e,i):Y(s,"add",e,i),this},delete(e){let t=ek(this),{has:i,get:s}=ec(t),r=i.call(t,e);r||(e=ek(e),r=i.call(t,e)),s&&s.call(t,e);let n=t.delete(e);return r&&Y(t,"delete",e,void 0),n},clear(){let e=ek(this),t=0!==e.size,i=e.clear();return t&&Y(e,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{i[s]=function(...i){let r=this.__v_raw,n=ek(r),l=h(n),o="entries"===s||s===Symbol.iterator&&l,a=r[s](...i),u=t?eu:e?eO:eD;return e||H(n,"iterate","keys"===s&&l?M:K),{next(){let{value:e,done:t}=a.next();return t?{value:e,done:t}:{value:o?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}),i}(e,t);return(t,s,r)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(c(i,s)&&s in t?i:t,s,r)}let ep={get:eh(!1,!1)},ed={get:eh(!1,!0)},e_={get:eh(!0,!1)},ev={get:eh(!0,!0)},eg=new WeakMap,ey=new WeakMap,eR=new WeakMap,eb=new WeakMap;function ew(e){return eT(e)?e:eE(e,!1,en,ep,eg)}function eS(e){return eE(e,!0,el,e_,eR)}function eE(e,t,i,s,r){var n;if(!d(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let l=(n=e).__v_skip||!Object.isExtensible(n)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(v(n).slice(8,-1));if(0===l)return e;let o=r.get(e);if(o)return o;let a=new Proxy(e,2===l?s:i);return r.set(e,a),a}function ex(e){return eT(e)?ex(e.__v_raw):!!(e&&e.__v_isReactive)}function eT(e){return!!(e&&e.__v_isReadonly)}function em(e){return!!(e&&e.__v_isShallow)}function eA(e){return!!e&&!!e.__v_raw}function ek(e){let t=e&&e.__v_raw;return t?ek(t):e}let eD=e=>d(e)?ew(e):e,eO=e=>d(e)?eS(e):e;function eI(e){return!!e&&!0===e.__v_isRef}function eL(e){return ej(e,!1)}function ej(e,t){return eI(e)?e:new eC(e,t)}class eC{constructor(e,t){this.dep=new N,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:ek(e),this._value=t?e:eD(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,i=this.__v_isShallow||em(e)||eT(e);y(e=i?e:ek(e),t)&&(this._rawValue=e,this._value=i?e:eD(e),this.dep.trigger())}}function eP(e){return eI(e)?e.value:e}let eW={get:(e,t,i)=>"__v_raw"===t?e:eP(Reflect.get(e,t,i)),set:(e,t,i,s)=>{let r=e[t];return eI(r)&&!eI(i)?(r.value=i,!0):Reflect.set(e,t,i,s)}};class eN{constructor(e){this.__v_isRef=!0,this._value=void 0;let t=this.dep=new N,{get:i,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=i,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}class eV{constructor(e,t,i){this._object=e,this._key=t,this._defaultValue=i,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){let i=V.get(e);return i&&i.get(t)}(ek(this._object),this._key)}}class eK{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function eM(e,t,i){let s=e[t];return eI(s)?s:new eV(e,t,i)}class eU{constructor(e,t,i){this.fn=e,this.setter=t,this._value=void 0,this.dep=new N(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=P-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=i}notify(){if(this.flags|=16,!(8&this.flags)&&i!==this)return E(this,!0),!0}get value(){let e=this.dep.track();return k(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}let eH={},eY=new WeakMap;function eG(e,t=!1,i=n){if(i){let t=eY.get(i);t||eY.set(i,t=[]),t.push(e)}}function eF(e,t=1/0,i){if(t<=0||!d(e)||e.__v_skip||(i=i||new Set).has(e))return e;if(i.add(e),t--,eI(e))eF(e.value,t,i);else if(f(e))for(let s=0;s<e.length;s++)eF(e[s],t,i);else if("[object Set]"===v(e)||h(e))e.forEach(e=>{eF(e,t,i)});else if("[object Object]"===v(e)){for(let s in e)eF(e[s],t,i);for(let s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&eF(e[s],t,i)}return e}return e.ARRAY_ITERATE_KEY=U,e.EffectFlags={ACTIVE:1,1:"ACTIVE",RUNNING:2,2:"RUNNING",TRACKING:4,4:"TRACKING",NOTIFIED:8,8:"NOTIFIED",DIRTY:16,16:"DIRTY",ALLOW_RECURSE:32,32:"ALLOW_RECURSE",PAUSED:64,64:"PAUSED",EVALUATED:128,128:"EVALUATED"},e.EffectScope=R,e.ITERATE_KEY=K,e.MAP_KEY_ITERATE_KEY=M,e.ReactiveEffect=w,e.ReactiveFlags={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw",IS_REF:"__v_isRef"},e.TrackOpTypes={GET:"get",HAS:"has",ITERATE:"iterate"},e.TriggerOpTypes={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},e.WatchErrorCodes={WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP"},e.computed=function(e,t,i=!1){let s,r;return"function"==typeof e?s=e:(s=e.get,r=e.set),new eU(s,r,i)},e.customRef=function(e){return new eN(e)},e.effect=function(e,t){e.effect instanceof w&&(e=e.effect.fn);let i=new w(e);t&&a(i,t);try{i.run()}catch(e){throw i.stop(),e}let s=i.run.bind(i);return s.effect=i,s},e.effectScope=function(e){return new R(e)},e.enableTracking=function(){I.push(O),O=!0},e.getCurrentScope=function(){return t},e.getCurrentWatcher=function(){return n},e.isProxy=eA,e.isReactive=ex,e.isReadonly=eT,e.isRef=eI,e.isShallow=em,e.markRaw=function(e){return!c(e,"__v_skip")&&Object.isExtensible(e)&&((e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})})(e,"__v_skip",!0),e},e.onEffectCleanup=function(e,t=!1){i instanceof w&&(i.cleanup=e)},e.onScopeDispose=function(e,i=!1){t&&t.cleanups.push(e)},e.onWatcherCleanup=eG,e.pauseTracking=L,e.proxyRefs=function(e){return ex(e)?e:new Proxy(e,eW)},e.reactive=ew,e.reactiveReadArray=G,e.readonly=eS,e.ref=eL,e.resetTracking=j,e.shallowReactive=function(e){return eE(e,!1,eo,ed,ey)},e.shallowReadArray=F,e.shallowReadonly=function(e){return eE(e,!0,ea,ev,eb)},e.shallowRef=function(e){return ej(e,!0)},e.stop=function(e){e.effect.stop()},e.toRaw=ek,e.toReactive=eD,e.toReadonly=eO,e.toRef=function(e,t,i){return eI(e)?e:"function"==typeof e?new eK(e):d(e)&&arguments.length>1?eM(e,t,i):eL(e)},e.toRefs=function(e){let t=f(e)?Array(e.length):{};for(let i in e)t[i]=eM(e,i);return t},e.toValue=function(e){return"function"==typeof e?e():eP(e)},e.track=H,e.traverse=eF,e.trigger=Y,e.triggerRef=function(e){e.dep&&e.dep.trigger()},e.unref=eP,e.watch=function(e,i,s=l){let r,a,u,c,{immediate:h,deep:p,once:d,scheduler:_,augmentJob:v,call:g}=s,R=e=>p?e:em(e)||!1===p||0===p?eF(e,1):eF(e),b=!1,S=!1;if(eI(e)?(a=()=>e.value,b=em(e)):ex(e)?(a=()=>R(e),b=!0):f(e)?(S=!0,b=e.some(e=>ex(e)||em(e)),a=()=>e.map(e=>eI(e)?e.value:ex(e)?R(e):"function"==typeof e?g?g(e,2):e():void 0)):a="function"==typeof e?i?g?()=>g(e,2):e:()=>{if(u){L();try{u()}finally{j()}}let t=n;n=r;try{return g?g(e,3,[c]):e(c)}finally{n=t}}:o,i&&p){let e=a,t=!0===p?1/0:p;a=()=>eF(e(),t)}let E=t,x=()=>{r.stop(),E&&E.active&&((e,t)=>{let i=e.indexOf(t);i>-1&&e.splice(i,1)})(E.effects,r)};if(d&&i){let e=i;i=(...t)=>{e(...t),x()}}let T=S?Array(e.length).fill(eH):eH,m=e=>{if(1&r.flags&&(r.dirty||e))if(i){let e=r.run();if(p||b||(S?e.some((e,t)=>y(e,T[t])):y(e,T))){u&&u();let t=n;n=r;try{let t=[e,T===eH?void 0:S&&T[0]===eH?[]:T,c];T=e,g?g(i,3,t):i(...t)}finally{n=t}}}else r.run()};return v&&v(m),(r=new w(a)).scheduler=_?()=>_(m,!1):m,c=e=>eG(e,!1,r),u=r.onStop=()=>{let e=eY.get(r);if(e){if(g)g(e,4);else for(let t of e)t();eY.delete(r)}},i?h?m(!0):T=r.run():_?_(m.bind(null,!0),!0):r.run(),x.pause=r.pause.bind(r),x.resume=r.resume.bind(r),x.stop=x,x},e}({});
