<template>
	<!-- 顶部导航栏 -->
	<n-layout-header bordered class="header">
		<div class="header-content">
			<h1>民建会员信息系统</h1>
			<div class="header-actions">
				<n-button text class="header-btn" @click="handleChangePassword">返回首页</n-button>
				<n-button text class="header-btn" @click="showChangePasswordModal = true">修改密码</n-button>
				<span class="username">jiangxizhou</span>
			</div>
		</div>
	</n-layout-header>

	<!-- 修改密码模态框 -->
	<n-modal v-model:show="showChangePasswordModal" preset="dialog" title="修改密码">
		<n-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef">
			<n-form-item label="原密码" path="oldPassword">
				<n-input v-model:value="passwordForm.oldPassword" type="password" placeholder="请输入原密码" />
			</n-form-item>
			<n-form-item label="新密码" path="newPassword">
				<n-input v-model:value="passwordForm.newPassword" type="password" placeholder="请输入新密码" />
			</n-form-item>
			<n-form-item label="确认新密码" path="confirmPassword">
				<n-input v-model:value="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码" />
			</n-form-item>
		</n-form>
		<template #action>
			<n-button @click="showChangePasswordModal = false">取消</n-button>
			<n-button @click="handleSubmitPassword" type="primary">确认修改</n-button>
		</template>
	</n-modal>
	<!-- 主体布局 -->
	<n-layout class="layout" has-sider>
		<!-- 左侧菜单栏 -->
		<SideMenu @update:value="handleMenuSelect" />

		<!-- 主内容区 -->
		<n-layout>
			<n-layout-content class="content">
				<component :is="currentComponent" />
				<div class="content-header-container">
					<div class="content-title-container">
						<div class="content-title-box">
							<span class="content-title">会员信息</span>
						</div>
					</div>
					<div class="content-actions">
						<n-button text class="action-btn">会员报表</n-button>
						<n-button text class="action-btn">导出信封贴头</n-button>
						<n-button text class="action-btn">导出简历</n-button>
						<n-button text class="action-btn">发文</n-button>
						<!-- 						<n-button type="success" size="small" class="import-btn">下载导入模版</n-button> -->
					</div>
				</div>
				<div class="main-container">
					<!-- 左侧会员信息列表 -->
					<div class="member-list">
						<n-card class="list-card">
							<n-data-table :columns="columns" :data="tableData" :pagination="false" bordered
								:row-class-name="rowClassName" class="data-table" />

							<!-- 分页控件 -->
							<div class="pagination">
								<div class="page-numbers">
									<n-button quaternary size="small" @click="goToPage(1)">1</n-button>
									<n-button quaternary size="small" @click="goToPage(2)">2</n-button>
									<n-button quaternary size="small" @click="goToPage(3)">3</n-button>
									<span class="ellipsis">...</span>
									<n-button quaternary size="small" @click="goToPage(28)">28</n-button>
								</div>

								<div class="page-jump">
									<span>到第</span>
									<n-input-number v-model:value="currentPage" :min="1" :max="28" size="small"
										style="width: 60px; margin: 0 8px;" />
									<span>页</span>
									<n-button size="small" style="margin-left: 8px;"
										@click="goToPage(currentPage)">确定</n-button>
								</div>

								<div class="page-info">
									<span>共 {{ totalItems }} 条</span>
									<n-select v-model:value="pageSize" :options="pageSizeOptions" size="small"
										style="width: 100px; margin-left: 16px;" />
								</div>
							</div>
						</n-card>
					</div>

					<!-- 右侧查询和操作区域 -->
					<div class="query-panel">

						<n-card class="panel-card">
							<!-- 查询选项卡 -->
							<div class="query-tabs">
								<div v-for="tab in tabs" :key="tab.value"
									:class="['tab-item', { active: activeTab === tab.value }]"
									@click="activeTab = tab.value">
									{{ tab.label }}
								</div>
							</div>

							<!-- 查询条件 -->
							<div class="query-form">
								<n-form label-placement="left" label-width="auto">
									<!-- 排序选项 -->
									<n-form-item label="排序选项">
										<div class="form-item-content">
											<n-select v-model:value="queryParams.sortOption" placeholder="请选择"
												:options="sortOptions" style="width: 120px;" />
											<n-button size="small" class="asc-btn">升序</n-button>
										</div>
									</n-form-item>

									<!-- 职务组织 -->
									<n-form-item label="职务组织">
										<div class="form-item-content">
											<n-checkbox v-model:checked="queryParams.includeSubOrg">含下级组织</n-checkbox>
											<n-button size="small">选择组织</n-button>
										</div>
									</n-form-item>

									<!-- 会员编号 -->
									<n-form-item label="会员编号">
										<div class="form-item-content">
											<n-select v-model:value="queryParams.memberIdOperator"
												:options="operatorOptions" style="width: 60px;" />
											<n-input v-model:value="queryParams.memberId" placeholder="请输入"
												style="flex: 1;" />
										</div>
									</n-form-item>

									<!-- 姓名 -->
									<n-form-item label="姓名">
										<n-input v-model:value="queryParams.name" placeholder="请输入" />
									</n-form-item>

									<!-- 性别 -->
									<n-form-item label="性别">
										<n-select v-model:value="queryParams.gender" placeholder="请选择"
											:options="genderOptions" />
									</n-form-item>

									<!-- 单位及职务 -->
									<n-form-item label="单位及职务">
										<n-input v-model:value="queryParams.position" placeholder="请输入" />
									</n-form-item>

									<!-- 年龄 -->
									<n-form-item label="年龄">
										<div class="form-item-content">
											<n-select v-model:value="queryParams.ageOperator" :options="operatorOptions"
												style="width: 60px;" />
											<n-input-number v-model:value="queryParams.age" placeholder="请输入"
												style="flex: 1;" />
										</div>
									</n-form-item>

									<!-- 人大政协职务 -->
									<n-form-item label="人大政协职务">
										<n-input v-model:value="queryParams.npcPosition" placeholder="请输入" />
									</n-form-item>

									<!-- 是否在职 -->
									<n-form-item label="是否在职">
										<n-radio-group v-model:value="queryParams.isActive">
											<n-space>
												<n-radio value="yes">是</n-radio>
												<n-radio value="no">否</n-radio>
												<n-radio value="all">全部</n-radio>
											</n-space>
										</n-radio-group>
									</n-form-item>

									<!-- 出生日期 -->
									<n-form-item label="出生日期">
										<div class="form-item-content">
											<n-select v-model:value="queryParams.birthDateOperator"
												:options="operatorOptions" style="width: 60px;" />
											<n-date-picker v-model:value="queryParams.birthDate" type="date"
												placeholder="yyyy-MM-dd" style="flex: 1;" />
										</div>
									</n-form-item>

									<!-- 操作按钮 -->
									<div class="form-actions">
										<n-button type="primary" class="query-btn" @click="handleQuery">
											查询
										</n-button>
										<n-button class="more-btn" @click="toggleMoreQuery">
											{{ showMoreQuery ? '收起' : '更多' }}
										</n-button>
										<n-button class="clear-btn" @click="handleClear">
											清空
										</n-button>
									</div>
								</n-form>
							</div>
						</n-card>
					</div>
				</div>
			</n-layout-content>
		</n-layout>
	</n-layout>
</template>

<script setup>
	import {
		useRouter
	} from 'vue-router';
	import {
		ref,
		h,
		defineAsyncComponent
	} from 'vue';
	import SideMenu from '../../components/SideMenu.vue';
	import {
		NLayout,
		NLayoutHeader,
		NLayoutSider,
		NLayoutContent,
		NMenu,
		NButton,
		NCard,
		NDataTable,
		NInput,
		NInputNumber,
		NSelect,
		NCheckbox,
		NRadioGroup,
		NRadio,
		NSpace,
		NDatePicker,
		NForm,
		NFormItem,
		NModal
	} from 'naive-ui';

	// 当前选中的选项卡
	const activeTab = ref('query');
	const showMoreQuery = ref(false);

	// 分页相关
	const currentPage = ref(1);
	const pageSize = ref(15);
	const totalItems = ref(414);
	const showChangePasswordModal = ref(false);

	// 密码表单
	const passwordForm = ref({
		oldPassword: '',
		newPassword: '',
		confirmPassword: ''
	});

	// 密码验证规则
	const passwordRules = {
		oldPassword: {
			required: true,
			message: '请输入原密码',
			trigger: 'blur'
		},
		newPassword: {
			required: true,
			message: '请输入新密码',
			trigger: 'blur'
		},
		confirmPassword: {
			required: true,
			message: '请再次输入新密码',
			trigger: 'blur',
			validator: (rule, value) => {
				if (value !== passwordForm.value.newPassword) {
					return new Error('两次输入的密码不一致');
				}
				return true;
			}
		}
	};

	const passwordFormRef = ref(null);

	// 查询参数
	const queryParams = ref({
		sortOption: null,
		includeSubOrg: true,
		memberId: '',
		memberIdOperator: '=',
		name: '',
		gender: null,
		position: '',
		age: null,
		ageOperator: '=',
		npcPosition: '',
		isActive: 'all',
		birthDate: null,
		birthDateOperator: '='
	});

	// 选项卡
	const tabs = [{
			label: '查询',
			value: 'query'
		},
		{
			label: '复杂查询',
			value: 'advanced'
		},
		{
			label: '导出',
			value: 'export'
		}
	];

	// 下拉选项
	const sortOptions = [{
			label: '默认排序',
			value: 'default'
		},
		{
			label: '按编号排序',
			value: 'id'
		},
		{
			label: '按姓名排序',
			value: 'name'
		}
	];

	const operatorOptions = [{
			label: '=',
			value: '='
		},
		{
			label: '>',
			value: '>'
		},
		{
			label: '<',
			value: '<'
		}
	];

	const genderOptions = [{
			label: '男',
			value: 'male'
		},
		{
			label: '女',
			value: 'female'
		}
	];

	const pageSizeOptions = [{
			label: '15 条/页',
			value: 15
		},
		{
			label: '20 条/页',
			value: 20
		},
		{
			label: '50 条/页',
			value: 50
		}
	];



	// 表格列定义
	const columns = [{
			title: '会员编号',
			key: 'memberId',
			align: 'center',
			width: 120
		},
		{
			title: '会员姓名',
			key: 'name',
			align: 'center',
			width: 120
		},
		{
			title: '组织名称',
			key: 'organization',
			align: 'center'
		},
		{
			title: '操作',
			key: 'actions',
			align: 'center',
			width: 100,
			render(row) {
				return h(
					NButton, {
						type: 'error',
						size: 'small',
						onClick: () => handleEdit(row)
					}, {
						default: () => '修改'
					}
				);
			}
		}
	];

	// 表格数据
	const tableData = ref([{
			memberId: '27997',
			name: '刘桂竹',
			organization: '赣州市委员会'
		},
		{
			memberId: '31454',
			name: '熊佳泉',
			organization: '赣州市委员会'
		},
		{
			memberId: '33105',
			name: '蔡声明',
			organization: '赣州市委员会'
		},
		{
			memberId: '34471',
			name: '熊小钧',
			organization: '赣州市委员会'
		},
		{
			memberId: '35564',
			name: '熊文亮',
			organization: '赣州市委员会'
		},
		{
			memberId: '35566',
			name: '熊家飞',
			organization: '赣州市委员会'
		},
		{
			memberId: '37190',
			name: '王英芳',
			organization: '赣州市委员会'
		},
		{
			memberId: '37799',
			name: '许伟生',
			organization: '赣州市委员会'
		},
		{
			memberId: '37853',
			name: '卓国梅',
			organization: '赣州市委员会'
		},
		{
			memberId: '40508',
			name: '李铭义',
			organization: '赣州市委员会'
		}
	]);

	// 行样式
	const rowClassName = (row, index) => {
		return index % 2 === 0 ? 'even-row' : 'odd-row';
	};

	// 处理菜单选择事件
	const currentComponent = ref(null);

	const handleMenuSelect = (key) => {
		currentComponent.value = defineAsyncComponent(() => import(`../${key}/index.vue`));
	};

	const router = useRouter();
	const handleChangePassword = () => {
		localStorage.removeItem('isAuthenticated');
		router.push('/login');
	};

	const handleEdit = (row) => {
		console.log('编辑:', row);
	};

	const goToPage = (page) => {
		currentPage.value = page;
		// 这里添加加载数据的逻辑
	};

	const handleQuery = () => {
		console.log('查询:', queryParams.value);
		// 这里添加查询逻辑
	};

	const toggleMoreQuery = () => {
		showMoreQuery.value = !showMoreQuery.value;
	};

	const handleClear = () => {
		queryParams.value = {
			sortOption: null,
			includeSubOrg: true,
			memberId: '',
			memberIdOperator: '=',
			name: '',
			gender: null,
			position: '',
			age: null,
			ageOperator: '=',
			npcPosition: '',
			isActive: 'all',
			birthDate: null,
			birthDateOperator: '='
		};
	};

	// 处理密码修改提交
	const handleSubmitPassword = () => {
		// 这里添加密码修改逻辑
		console.log('修改密码:', passwordForm.value);
		showChangePasswordModal.value = false;
	};
</script>

<style scoped>
	/* 全局样式 */
	* {
		box-sizing: border-box;
		margin: 0;
		padding: 0;
		font-family: "Microsoft YaHei", sans-serif;
	}

	/* 修改后的内容头部样式 */
	.content-header-container {
		display: flex;
		align-items: center;
		margin-bottom: 16px;
		width: 100%;
		/* 确保占满可用宽度 */
	}

	.content-title-container {
		flex: 1;
		/* 占据剩余空间 */
		min-width: 0;
		/* 防止内容溢出 */
	}

	.content-title-box {
		padding: 8px 16px;
		background-color: white;
		border-radius: 4px;
		box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
		display: inline-block;
		/* 根据内容自适应宽度 */
		width: 100%;
		/* 占满父容器 */
	}

	.content-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
	}

	.content-actions {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.action-btn {
		color: #333;
		padding: 0 8px;
		font-size: 15px;
	}

	.action-btn:hover {
		color: #c8102e;
		background-color: transparent !important;
	}

	.import-btn {
		background-color: #52c41a;
		border-radius: 4px;
	}

	/* 调整主布局间距 */
	.main-layout {
		margin-top: 0;
	}

	/* 顶部导航栏 */
	.header {
		height: 60px;
		background-color: #c8102e;
		padding: 0 24px;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100%;
		max-width: 1600px;
		margin: 0 auto;
	}

	.header h1 {
		color: white;
		font-size: 18px;
		font-weight: bold;
		margin: 0;
	}

	.header-actions {
		display: flex;
		align-items: center;
		gap: 16px;
	}

	.header-btn {
		color: white !important;
	}

	.username {
		color: white;
		margin-left: 8px;
	}

	/* 主体布局 */
	.layout {
		height: 100vh;
		display: flex;
		margin-top: 60px;
	}

	/* 左侧菜单栏 */
	.sider {
		height: calc(100vh - 60px);
		background-color: white;
	}

	/* 主内容区 */
	.content {
		padding: 20px;
		background-color: #f5f7fa;
		height: calc(100vh - 60px);
		overflow-y: auto;
	}

	.main-container {
		display: flex;
		gap: 20px;
		max-width: 1600px;
		margin: 0 auto;
	}

	/* 左侧会员列表 */
	.member-list {
		flex: 1;
		min-width: 0;
		width: 100%;
		/* 确保占满可用宽度 */
	}

	.list-card {
		height: 100%;
		border-radius: 8px;
	}

	.data-table {
		width: 100%;
		/* 表格占满卡片 */
	}

	/* 分页控件 */
	.pagination {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 16px;
		padding: 12px 0;
		border-top: 1px solid #eaeaea;
	}

	.page-numbers {
		display: flex;
		align-items: center;
		gap: 4px;
	}

	.ellipsis {
		padding: 0 8px;
	}

	.page-jump {
		display: flex;
		align-items: center;
	}

	.page-info {
		display: flex;
		align-items: center;
	}

	/* 右侧查询面板 */
	.query-panel {
		width: 380px;
		flex-shrink: 0;
	}

	.panel-card {
		height: 100%;
		border-radius: 8px;
	}

	/* 顶部操作按钮 */
	.action-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		margin-bottom: 16px;
		padding-bottom: 16px;
		border-bottom: 1px solid #eaeaea;
	}

	.action-btn {
		color: #333;
	}

	.import-btn {
		background-color: #52c41a;
	}

	/* 查询选项卡 */
	.query-tabs {
		display: flex;
		margin-bottom: 16px;
		border-bottom: 1px solid #eaeaea;
	}

	.tab-item {
		padding: 8px 16px;
		cursor: pointer;
		color: #666;
	}

	.tab-item.active {
		color: #c8102e;
		border-bottom: 2px solid #c8102e;
	}

	/* 查询表单 */
	.query-form {
		padding: 8px 0;
	}

	.form-item-content {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.asc-btn {
		background-color: #f0f0f0;
		color: #333;
	}

	/* 操作按钮 */
	.form-actions {
		display: flex;
		justify-content: flex-end;
		gap: 12px;
		margin-top: 16px;
	}

	.query-btn {
		background-color: #c8102e;
		color: white;
		width: 50px;
	}

	.more-btn {
		background-color: #f0f0f0;
		color: #333;
		width: 50px;
	}

	.clear-btn {
		background-color: #f0f0f0;
		color: #333;
		width: 50px;
	}

	/* 表格行样式 */
	:deep(.even-row) {
		background-color: #fafafa;
	}

	:deep(.odd-row) {
		background-color: white;
	}

	:deep(.n-data-table-th) {
		background-color: #f5f7fa;
		font-weight: bold;
		text-align: center;
	}

	:deep(.n-data-table-td) {
		text-align: center;
	}

	/* 响应式布局 */
	@media (max-width: 1200px) {
		.main-container {
			flex-direction: column;
		}

		.query-panel {
			width: 100%;
		}
	}
</style>